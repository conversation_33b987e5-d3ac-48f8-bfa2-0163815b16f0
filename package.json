{"name": "<PERSON><PERSON><PERSON><PERSON>-next", "version": "0.1.0", "private": true, "scripts": {"build": "next build --no-lint", "dev": "next dev --turbopack", "lint": "next lint", "start": "next start", "migrate:dev": "dotenv -e .env.local -- prisma migrate dev", "migrate:deploy": "dotenv -e .env.local -- prisma migrate deploy", "migrate:reset": "dotenv -e .env.local -- prisma migrate reset", "migrate:status": "dotenv -e .env.local -- prisma migrate status", "db:studio": "dotenv -e .env.local -- prisma studio", "db:seed": "dotenv -e .env.local -- ts-node --compiler-options \"{\\\"module\\\":\\\"CommonJS\\\"}\" prisma/seed.ts", "db:apply-policies": "dotenv -e .env.local -- ts-node supabase/scripts/apply-policies.ts", "db:setup-infrastructure": "npm run db:setup-extensions && npm run db:setup-functions && npm run db:setup-cron", "db:setup-extensions": "dotenv -e .env.local -- sh -c 'psql \"$DIRECT_URL\" -f supabase/infrastructure/extensions.sql'", "db:setup-functions": "dotenv -e .env.local -- sh -c 'psql \"$DIRECT_URL\" -f supabase/infrastructure/functions.sql'", "db:setup-cron": "dotenv -e .env.local -- sh -c 'psql \"$DIRECT_URL\" -f supabase/infrastructure/cron-jobs.sql'", "db:setup-migrations": "npm run db:run-migration-002", "db:run-migration-002": "dotenv -e .env.local -- sh -c 'psql \"$DIRECT_URL\" -f supabase/migrations/002_auction_expiration_cron.sql'", "db:setup-config": "dotenv -e .env.local -- ts-node supabase/scripts/setup-database-config.ts", "db:deploy-functions": "supabase functions deploy sendAuctionNotification", "db:rebuild": "npm run migrate:reset -- --force && npm run db:setup-infrastructure && npm run db:setup-migrations && (npm run db:setup-config || echo 'Config setup failed - continuing...') && (npm run db:deploy-functions || echo 'Edge function deployment skipped - continuing...') && npm run db:apply-policies && npm run db:seed", "db:reset-complete": "dotenv -e .env.local -- ts-node supabase/scripts/reset-database-complete.ts", "postinstall": "prisma generate"}, "dependencies": {"@aws-sdk/client-s3": "^3.840.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.10.1", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.39.7", "@t3-oss/env-nextjs": "^0.9.2", "@tanstack/react-query": "^4.36.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "lucide-react": "^0.488.0", "next": "^15.3.0", "next-themes": "^0.4.6", "pg": "^8.16.3", "react": "18.2.0", "react-day-picker": "^9.7.0", "react-dom": "18.2.0", "react-hook-form": "^7.55.0", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.12", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2", "zod-to-json-schema": "^3.23.2"}, "devDependencies": {"@types/eslint": "^9.6.0", "@types/node": "^20.11.20", "@types/pg": "^8.15.4", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "autoprefixer": "^10.4.16", "dotenv": "^16.4.5", "dotenv-cli": "^8.0.0", "eslint": "^9.0.0", "eslint-config-next": "^15.3.0", "postcss": "^8.4.32", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "prisma": "^6.14.0", "prisma-zod-generator": "^0.8.13", "tailwindcss": "^3.4.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "ct3aMetadata": {"initVersion": "7.26.0"}}