#!/usr/bin/env ts-node

/**
 * Complete Database Reset Script
 * 
 * This script completely resets everything in Supabase:
 * - Deletes all edge functions
 * - Removes all cron jobs
 * - Drops all custom database functions
 * - Deletes all authentication users
 * - Resets database schema and data via Prisma
 * 
 * Usage:
 *   ts-node supabase/reset-database-complete.ts
 * 
 * Environment variables required:
 *   - SUPABASE_ACCESS_TOKEN: Access token for Supabase Management API
 *   - NEXT_PUBLIC_SUPABASE_URL: Supabase project URL
 *   - SUPABASE_SERVICE_ROLE_KEY: Service role key for auth operations
 *   - DIRECT_URL: Direct database connection URL
 */

import { execSync } from 'child_process';
import * as path from 'path';
import * as https from 'https';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') });

interface SupabaseResponse {
  [key: string]: any;
}

interface EdgeFunction {
  id: string;
  name: string;
  status: string;
}



function validateEnvironmentVariables(): void {
  const required = [
    'SUPABASE_ACCESS_TOKEN',
    'NEXT_PUBLIC_SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY',
    'DIRECT_URL'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(key => console.error(`   - ${key}`));
    console.error('\nPlease check your .env.local file and ensure all required variables are set.');
    process.exit(1);
  }

  console.log('✅ All required environment variables are present');
}

function getProjectRef(): string | null {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (!url) return null;

  const match = url.match(/https:\/\/([^.]+)\.supabase\.co/);
  return match && match[1] ? match[1] : null;
}

function makeSupabaseRequest(method: string, endpoint: string, data: any = null): Promise<SupabaseResponse> {
  return new Promise((resolve, reject) => {
    const projectRef = getProjectRef();
    if (!projectRef) {
      reject(new Error('Could not extract project reference from NEXT_PUBLIC_SUPABASE_URL'));
      return;
    }

    const options: https.RequestOptions = {
      hostname: 'api.supabase.com',
      port: 443,
      path: `/v1/projects/${projectRef}${endpoint}`,
      method: method,
      headers: {
        'Authorization': `Bearer ${process.env.SUPABASE_ACCESS_TOKEN}`,
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = responseData ? JSON.parse(responseData) : {};
          if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsed);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${responseData}`));
          }
        } catch (error) {
          resolve(responseData as any);
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

function makeSupabaseAdminRequest(method: string, endpoint: string, data: any = null): Promise<SupabaseResponse> {
  return new Promise((resolve, reject) => {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!serviceRoleKey) {
      reject(new Error('SUPABASE_SERVICE_ROLE_KEY is required for auth operations'));
      return;
    }
    
    if (!supabaseUrl) {
      reject(new Error('NEXT_PUBLIC_SUPABASE_URL is required'));
      return;
    }
    
    const url = new URL(supabaseUrl);
    const options: https.RequestOptions = {
      hostname: url.hostname,
      port: 443,
      path: `/auth/v1/admin${endpoint}`,
      method: method,
      headers: {
        'Authorization': `Bearer ${serviceRoleKey}`,
        'Content-Type': 'application/json',
        'apikey': serviceRoleKey
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = responseData ? JSON.parse(responseData) : {};
          if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsed);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${responseData}`));
          }
        } catch (error) {
          resolve(responseData as any);
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function deleteEdgeFunctions(): Promise<void> {
  console.log('🗑️  Deleting edge functions...');
  
  try {
    const functions = await makeSupabaseRequest('GET', '/functions') as EdgeFunction[];
    
    if (functions && functions.length > 0) {
      console.log(`   Found ${functions.length} edge function(s) to delete`);
      
      for (const func of functions) {
        try {
          console.log(`   Deleting function: ${func.name}`);
          await makeSupabaseRequest('DELETE', `/functions/${func.id}`);
          console.log(`   ✅ Deleted: ${func.name}`);
        } catch (deleteError: any) {
          if (deleteError.message.includes('404') || deleteError.message.includes('Function not found')) {
            console.log(`   ℹ️  Function ${func.name} not found via Management API (this is normal)`);
          } else {
            console.warn(`   ⚠️  Could not delete function ${func.name}: ${deleteError.message}`);
          }
        }
      }
      console.log(`✅ Processed ${functions.length} edge function(s)`);
    } else {
      console.log('✅ No edge functions to delete');
    }
  } catch (error: any) {
    console.warn(`⚠️  Could not delete edge functions: ${error.message}`);
  }
}

function deleteCronJobs(): void {
  console.log('🗑️  Deleting cron jobs...');
  
  try {
    const query = `
      SELECT cron.unschedule(jobname) 
      FROM cron.job 
      WHERE jobname IN (
        'close-expired-auctions',
        'auction-notifications', 
        'comprehensive-auction-notifications'
      );
    `;
    
    execSync(`psql "${process.env.DIRECT_URL}" -c "${query}"`, { stdio: 'inherit' });
    console.log('✅ Cron jobs deleted');
  } catch (error: any) {
    console.warn(`⚠️  Could not delete cron jobs: ${error.message}`);
  }
}

function deleteCustomFunctions(): void {
  console.log('🗑️  Deleting custom database functions...');
  
  try {
    const query = `
      DROP FUNCTION IF EXISTS public.close_expired_auctions_manual() CASCADE;
      DROP FUNCTION IF EXISTS public.trigger_auction_notifications() CASCADE;
      DROP FUNCTION IF EXISTS public.trigger_comprehensive_auction_notifications() CASCADE;
      DROP FUNCTION IF EXISTS public.trigger_notifications_manual(UUID[], TEXT) CASCADE;
      DROP FUNCTION IF EXISTS public.log_notification(UUID, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, INTEGER, JSONB) CASCADE;
      DROP FUNCTION IF EXISTS public.get_notification_stats(TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) CASCADE;
      DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;
      DROP FUNCTION IF EXISTS public.get_user_role(UUID) CASCADE;
      DROP FUNCTION IF EXISTS public.sync_user_profile() CASCADE;
    `;
    
    execSync(`psql "${process.env.DIRECT_URL}" -c "${query}"`, { stdio: 'inherit' });
    console.log('✅ Custom database functions deleted');
  } catch (error: any) {
    console.warn(`⚠️  Could not delete custom functions: ${error.message}`);
  }
}

async function deleteAuthUsers(): Promise<void> {
  console.log('🗑️  Deleting authentication users...');

  try {
    // Get list of users from Supabase Auth Admin API
    const response = await makeSupabaseAdminRequest('GET', '/users');

    if (response && response.users && response.users.length > 0) {
      console.log(`   Found ${response.users.length} users to delete`);

      for (const user of response.users) {
        try {
          console.log(`   Deleting user: ${user.email}`);
          await makeSupabaseAdminRequest('DELETE', `/users/${user.id}`);
          console.log(`   ✅ Deleted: ${user.email}`);
        } catch (deleteError: any) {
          console.warn(`   ⚠️  Could not delete user ${user.email}: ${deleteError.message}`);
        }
      }
      console.log(`✅ Processed ${response.users.length} authentication user(s)`);
    } else {
      console.log('✅ No authentication users to delete');
    }
  } catch (error: any) {
    console.warn(`⚠️  Could not delete authentication users: ${error.message}`);
  }
}

function resetPrismaDatabase(): void {
  console.log('🗑️  Resetting Prisma database...');

  try {
    execSync('npm run migrate:reset -- --force', { stdio: 'inherit' });
    console.log('✅ Prisma database reset complete');
  } catch (error: any) {
    console.error(`❌ Failed to reset Prisma database: ${error.message}`);
    throw error;
  }
}

async function main(): Promise<void> {
  console.log('🚀 Starting complete database reset...\n');
  console.log('⚠️  WARNING: This will delete ALL data, functions, cron jobs, edge functions, and auth users!\n');

  try {
    validateEnvironmentVariables();

    // Delete Supabase-specific resources
    await deleteEdgeFunctions();
    deleteCronJobs();
    deleteCustomFunctions();
    await deleteAuthUsers();

    // Reset database schema and data
    resetPrismaDatabase();

    console.log('\n🎉 Complete database reset finished!');
    console.log('🧹 All data, functions, cron jobs, edge functions, and auth users have been deleted');
    console.log('💡 You can now run "npm run db:rebuild" to set everything up again');

  } catch (error: any) {
    console.error('\n💥 Reset failed:', error.message);
    process.exit(1);
  }
}

// Execute the main function
main().catch((error) => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
